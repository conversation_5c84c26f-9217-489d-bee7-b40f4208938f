import ctypes
import logging
import os
import socket
import subprocess
import sys
import threading
import time

import psutil
import requests
import win32con
import win32gui
import win32process

# 配置
EXE_PATH = r"D:\code\chrome\Release (1)\指纹浏览器API接口.exe"
REMOTE_PORT = 2222
API_PORT = 9001
# 只隐藏这个特定的窗口标题
TARGET_WINDOW_TITLE = "指纹识别器API接口"
# 是否需要管理员权限（大多数情况下不需要）
REQUIRE_ADMIN = True

# 日志配置
LOG_FILE = "launcher.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE, encoding='utf-8'),
    ]
)
logger = logging.getLogger(__name__)


def ensure_pythonw():
    """确保使用 pythonw.exe 运行，避免命令行窗口"""
    if sys.executable.endswith('python.exe'):
        pythonw_path = sys.executable.replace('python.exe', 'pythonw.exe')
        if os.path.exists(pythonw_path):
            logger.info(f"切换到 pythonw.exe: {pythonw_path}")
            # 重新启动为 pythonw.exe
            subprocess.Popen([pythonw_path] + sys.argv)
            sys.exit(0)
        else:
            logger.warning("未找到 pythonw.exe，继续使用当前解释器")
    else:
        logger.info("已使用 pythonw.exe 运行")


def is_admin():
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False


def request_admin():
    if is_admin():
        return True

    logger.info("🔐 需要管理员权限来启动程序")
    try:
        ctypes.windll.shell32.ShellExecuteW(
            None, "runas", sys.executable, f'"{__file__}"', None, 1
        )
        return False
    except:
        logger.error("❌ 获取管理员权限失败")
        return False


def kill_process_on_port(port):
    """杀死占用指定端口的进程"""
    logger.info(f"检查端口 {port} 的占用情况...")

    for conn in psutil.net_connections():
        if conn.laddr.port == port and conn.status == 'LISTEN':
            try:
                process = psutil.Process(conn.pid)
                logger.info(f"发现占用端口 {port} 的进程: {process.name()} (PID: {conn.pid})")

                if "浏览器" in process.name() or "API" in process.name() or "chrome" in process.name().lower():
                    logger.info(f"终止进程: {process.name()}")
                    process.terminate()
                    time.sleep(2)
                    if process.is_running():
                        process.kill()
                    logger.info(f"✅ 进程已终止")
                    return True
            except Exception as e:
                logger.error(f"终止进程时出错: {e}")

    return False


def find_available_port(start_port=2223):
    """找一个可用的端口"""
    for port in range(start_port, start_port + 100):
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        if result != 0:
            logger.info(f"找到可用端口: {port}")
            return port
    logger.warning("未找到可用端口")
    return None


def hide_specific_window():
    """只隐藏指定的窗口"""
    def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if window_title:
                    # 获取窗口所属的进程ID
                    _, pid = win32process.GetWindowThreadProcessId(hwnd)
                    try:
                        process = psutil.Process(pid)
                        process_name = process.name()
                        windows.append((hwnd, window_title, pid, process_name))
                    except:
                        windows.append((hwnd, window_title, pid, "Unknown"))
            return True

    def find_and_hide():
            for attempt in range(20):  # 增加监控时间到20秒
                try:
                    windows = []
                    win32gui.EnumWindows(enum_windows_callback, windows)

                    # 记录所有可见窗口（调试用）
                    if attempt == 0:
                        logger.info("🔍 当前可见窗口列表（包含进程信息）：")
                        for hwnd, title, pid, process_name in windows:
                            logger.info(f"  - 标题: {title}")
                            logger.info(f"    进程: {process_name} (PID: {pid})")

                    # 查找目标窗口
                    target_hwnd = None
                    matched_title = None

                    for hwnd, title, pid, process_name in windows:
                        # 1. 精确匹配窗口标题
                        if title == TARGET_WINDOW_TITLE:
                            target_hwnd = hwnd
                            matched_title = title
                            logger.info(f"🎯 精确匹配找到窗口: {title}")
                            break

                        # 2. 匹配进程名（包含"API接口"的exe文件）
                        elif "API接口" in process_name:
                            target_hwnd = hwnd
                            matched_title = title
                            logger.info(f"🎯 通过进程名匹配找到窗口: {title} (进程: {process_name})")
                            break

                        # 3. 匹配窗口标题（排除浏览器标签页）
                        elif ("指纹" in title and "API" in title and
                              "Microsoft Edge" not in title and
                              "Google Chrome" not in title and
                              "Firefox" not in title):
                            target_hwnd = hwnd
                            matched_title = title
                            logger.info(f"🎯 通过标题匹配找到窗口: {title}")
                            break

                    if target_hwnd:
                        win32gui.ShowWindow(target_hwnd, win32con.SW_HIDE)
                        logger.info(f"✅ 已隐藏目标窗口: {matched_title}")
                        return True

                    if attempt == 0:
                        logger.info(f"⚠️ 第{attempt + 1}次未找到目标窗口，继续等待...")
                    elif attempt % 5 == 0:
                        logger.info(f"⚠️ 第{attempt + 1}次未找到目标窗口，继续等待...")

                except Exception as e:
                    logger.error(f"查找窗口时出错: {e}")
                time.sleep(1)

            logger.error(f"❌ 20秒内未能找到并隐藏窗口: {TARGET_WINDOW_TITLE}")
            return False

    return find_and_hide()


def start_with_selective_hide():
    """启动程序并只隐藏指定窗口（无闪现）"""

    def launch_hidden_process(cmd, exe_dir):
        """使用 Win32 API 隐藏启动目标窗口（避免闪现）"""
        si = win32process.STARTUPINFO()
        si.dwFlags |= win32con.STARTF_USESHOWWINDOW
        si.wShowWindow = win32con.SW_HIDE

        command_line = ' '.join(cmd)
        try:
            _, _, dwProcessId, _ = win32process.CreateProcess(
                None,
                command_line,
                None,
                None,
                False,
                0,
                None,
                exe_dir,
                si
            )
            logger.info(f"✅ 程序已隐藏启动，PID: {dwProcessId}")
            return psutil.Process(dwProcessId)
        except Exception as e:
            logger.error(f"❌ 使用隐藏模式启动失败: {e}")
            return None

    # 1. 处理端口占用
    remote_port = REMOTE_PORT

    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', remote_port))
    sock.close()

    if result == 0:
        logger.warning(f"⚠️ 端口 {remote_port} 被占用")
        if kill_process_on_port(remote_port):
            time.sleep(3)
        else:
            new_port = find_available_port(remote_port + 1)
            if new_port:
                logger.info(f"使用替代端口: {new_port}")
                remote_port = new_port
            else:
                logger.error("❌ 无法找到可用端口")
                return None

    # 2. 设置工作目录
    original_cwd = os.getcwd()
    exe_dir = os.path.dirname(EXE_PATH)

    try:
        os.chdir(exe_dir)
        logger.info(f"切换工作目录到: {exe_dir}")

        cmd = [EXE_PATH, f"--remote-debugging-port={remote_port}"]
        logger.info(f"启动命令: {' '.join(cmd)}")
        logger.info("🚀 隐藏启动程序...")

        # 使用隐藏窗口方式启动目标程序（无窗口闪现）
        process = launch_hidden_process(cmd, exe_dir)

        if not process:
            logger.error("❌ 启动失败")
            return None

        time.sleep(1)
        if process.status() != psutil.STATUS_RUNNING:
            logger.error("❌ 程序未能成功启动")
            return None

        # API健康检查（保持 print，不写日志）
        print("正在进行API健康检查...")
        api_url = f"http://localhost:{API_PORT}/api/browser/list"

        for i in range(30):
            if not process.is_running():
                print("❌ 程序意外退出")
                return None

            try:
                response = requests.get(api_url, timeout=3)
                if response.status_code == 200:
                    print(f"✅ API服务正常！(耗时: {i + 1}秒)")
                    print(f"API地址: {api_url}")
                    print(f"远程调试端口: {remote_port}")
                    return process
            except requests.exceptions.RequestException:
                pass

            if i % 10 == 0 and i > 0:
                print(f"仍在等待API服务... ({i + 1}/30)")
            time.sleep(1)

        print("❌ API服务未在30秒内响应")
        process.terminate()
        return None

    except Exception as e:
        logger.error(f"启动程序时出错: {e}")
        return None
    finally:
        os.chdir(original_cwd)


def show_target_window():
    """显示被隐藏的目标窗口"""
    try:
        def enum_windows_callback(hwnd, windows):
            window_title = win32gui.GetWindowText(hwnd)
            if window_title:
                windows.append((hwnd, window_title))
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        # 查找目标窗口
        target_hwnd = None
        matched_title = None

        for hwnd, title in windows:
            if title == TARGET_WINDOW_TITLE or ("指纹" in title and "API" in title):
                target_hwnd = hwnd
                matched_title = title
                break

        if target_hwnd:
            win32gui.ShowWindow(target_hwnd, win32con.SW_SHOW)
            win32gui.SetForegroundWindow(target_hwnd)
            print(f"✅ 已显示窗口: {matched_title}")
            return True
        else:
            print(f"❌ 未找到窗口: {TARGET_WINDOW_TITLE}")
            return False
    except Exception as e:
        logger.error(f"显示窗口时出错: {e}")
        return False


def hide_target_window():
    """隐藏目标窗口"""
    try:
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if window_title:
                    windows.append((hwnd, window_title))
            return True

        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)

        # 查找目标窗口
        target_hwnd = None
        matched_title = None

        for hwnd, title in windows:
            if title == TARGET_WINDOW_TITLE or ("指纹" in title and "API" in title):
                target_hwnd = hwnd
                matched_title = title
                break

        if target_hwnd:
            win32gui.ShowWindow(target_hwnd, win32con.SW_HIDE)
            print(f"✅ 已隐藏窗口: {matched_title}")
            return True
        else:
            print(f"❌ 未找到窗口: {TARGET_WINDOW_TITLE}")
            return False
    except Exception as e:
        logger.error(f"隐藏窗口时出错: {e}")
        return False


def monitor_process(process):
    """监控进程状态（健康检查不写日志）"""
    while process.poll() is None:
        time.sleep(30)

        try:
            response = requests.get(f"http://localhost:{API_PORT}/api/browser/list", timeout=5)
            if response.status_code == 200:
                print("✅ 程序运行正常")
            else:
                print(f"⚠️ API响应异常: {response.status_code}")
        except:
            print("⚠️ API无响应")


def main():
    # 只在需要时才要求管理员权限
    if REQUIRE_ADMIN and not is_admin():
        if not request_admin():
            return

    print("=== 指纹浏览器API启动器（选择性窗口隐藏版）===")
    print(f"目标窗口: {TARGET_WINDOW_TITLE}")
    if REQUIRE_ADMIN:
        print("✅ 已获得管理员权限")
    else:
        print("ℹ️ 以普通用户权限运行")

    if not os.path.exists(EXE_PATH):
        print(f"❌ 文件不存在: {EXE_PATH}")
        input("按回车键退出...")
        return

    # 启动程序并隐藏指定窗口
    process = start_with_selective_hide()

    if process:
        print(f"\n🎉 程序启动成功！")
        print(f"🔒 已隐藏目标窗口: {TARGET_WINDOW_TITLE}")
        print("其他窗口保持正常显示...")
        print("\n输入命令:")
        print("  'status' - 检查状态")
        print("  'show'   - 显示被隐藏的窗口")
        print("  'hide'   - 隐藏目标窗口")
        print("  'quit'   - 退出程序")

        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_process, args=(process,), daemon=True)
        monitor_thread.start()

        try:
            while process.poll() is None:
                try:
                    user_input = input("\n> ").strip().lower()

                    if user_input == 'quit' or user_input == 'exit':
                        break
                    elif user_input == 'status':
                        try:
                            response = requests.get(f"http://localhost:{API_PORT}/api/browser/list", timeout=3)
                            print(f"API状态: {'✅ 正常' if response.status_code == 200 else '❌ 异常'}")
                            print(f"进程状态: {'✅ 运行中' if process.poll() is None else '❌ 已退出'}")
                        except:
                            print("API状态: ❌ 无响应")
                    elif user_input == 'show':
                        show_target_window()
                    elif user_input == 'hide':
                        hide_target_window()

                except (EOFError, KeyboardInterrupt):
                    break

        except KeyboardInterrupt:
            print("\n收到退出信号...")

        # 清理
        if process.poll() is None:
            print("正在终止程序...")
            process.terminate()
            try:
                process.wait(timeout=5)
                print("✅ 程序已正常退出")
            except:
                try:
                    process.kill()
                    print("✅ 程序已强制终止")
                except:
                    pass
    else:
        print("\n❌ 程序启动失败")

    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
