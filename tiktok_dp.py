import DrissionPage


class TiktokDP:
    @staticmethod
    def get_homepage(page):
        """首页"""
        page.get("https://www.tiktok.com/")
        # page.wait.ele_displayed('xpath://*[@data-e2e="nav-profile"]/button/div', timeout=10).click()
        # page.wait.ele_displayed('xpath://*[@data-e2e="edit-profile-entrance"]', timeout=10).click()
        page.ele('xpath://*[@data-e2e="nav-profile"]/button/div').wait.displayed(timeout=10).click()
        page.ele('xpath://*[@data-e2e="edit-profile-entrance"]').wait.displayed(timeout=10).click()

    def change_profile(self, page, image):
        """改头像"""
        self.get_homepage(page)
        ele = page.wait.ele_displayed('xpath://*[@data-e2e="edit-profile-avatar"]/div/span/img', timeout=10)
        page.set.upload_files(image)
        ele.click()
        page.wait.upload_paths_inputted()
        page.ele('xpath://*[@data-e2e="edit-profile-popup"]/div[last()]/div[last()]/button[last()]', timeout=10).click()
        self.submit(page)

    def change_username(self, page, username):
        """改用户名"""
        self.get_homepage(page)
        page.wait.ele_displayed('xpath://*[@data-e2e="edit-profile-username-input"]/input', timeout=10).input(
            username, clear=True)
        self.submit(page)

    def change_nickname(self,page, new_nickname: str):
        """改昵称"""
        self.get_homepage(page)
        page.wait.ele_displayed('xpath://*[@data-e2e="edit-profile-name-input"]/input', timeout=10).input(new_nickname,
                                                                                                          clear=True)
        self.submit(page)

    def change_desc(self,page, desc):
        """改签名"""
        self.get_homepage(page)
        page.wait.ele_displayed('xpath://*[@data-e2e="edit-profile-bio-input"]', timeout=10).input(desc,
                                                                                                   clear=True)
        self.submit(page)

    def submit(self, page):
        """修改提交按钮"""
        page.ele('xpath://*[@data-e2e="edit-profile-popup"]/div[last()]/button[last()]').wait.displayed(timeout=10).click()


if __name__ == "__main__":
    browser = DrissionPage.Chromium(2222)
    pages = browser.get_tabs()
    page = pages[0]
    Tk = TiktokDP()
    # 改头像
    Tk.change_profile(page=page,new_avatar_path=r"D:\_mywork\tcp_server\static\imgs\0.jpeg")
    # 改用户名
    Tk.change_username(page=page,new_username="hqhqhqhqhq3664645245245")
    # 改昵称
    Tk.change_nickname(page=page,new_nickname="hqhqhqhqhq36646452425")
    # 改签名
    Tk.change_signatures(page=page,new_signatures="hello649648")
