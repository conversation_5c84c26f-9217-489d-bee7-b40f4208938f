from DrissionPage import Chromium

class FaceBookDP:
    @staticmethod
    def click_setting(page):
        """点击编辑按钮，进入设置页面"""
        page.ele("xpath://img[@src='https://static.xx.fbcdn.net/rsrc.php/v4/yW/r/OR6SzrfoMFg.png']").click()  # 编辑按钮
        page.wait.load_start()
        page.wait.eles_loaded("xpath://div[@role='dialog']", timeout=3)


    @staticmethod
    def runto_userhome(page):
        """进入facebook个人主页，每次进入facebook都可能会出现弹窗，需要判断是否有弹窗"""

        page.get("https://www.facebook.com/")
        page.wait.load_start()
        result = page.wait.eles_loaded("xpath://div[@role='alertdialog']", timeout=3)
        if result:
            page.eles("xpath://div[@role='alertdialog']")[0].click()

        page.eles("xpath://a[contains(@href,'https://www.facebook.com/profile.php?id=')]")[1].click()
        page.wait.load_start()


    @staticmethod
    def user_setting(page):
        """进入个人中心设置，默认第一个账户，有其它情况另外考虑"""

        page.get("https://accountscenter.facebook.com/profiles")
        page.wait.load_start()

        page.ele("xpath://div[@role='listitem']").click()
        page.wait.load_start()



    def change_profile_photo(self,page, image):
        """更改头像方法"""
        self.runto_userhome(page)
        self.click_setting(page)

        page.eles("xpath://div[@role='dialog']//div[@role='button' and @tabindex='0']")[1].click()

        page.wait.eles_loaded("xpath:((//div[@role='dialog'])[2]//div[@role='button' and @tabindex='0'])[2]", timeout=5)
        page.eles("xpath:(//div[@role='dialog'])[2]//div[@role='button' and @tabindex='0']")[1].click.to_upload(image)

        page.wait.eles_loaded("xpath://div[@aria-label='Save']", timeout=5)
        page.ele("xpath://div[@aria-label='Save']").click()


    def change_desc(self,page, desc):
        """更改个人简介方法"""
        self.runto_userhome(page)
        self.click_setting(page)

        page.eles("xpath://div[@role='dialog']//div[@role='button' and @tabindex='0']")[4].click()

        page.wait.eles_loaded("xpath://textarea[@aria-label='Enter bio text']", timeout=5)
        page.ele("xpath://textarea[@aria-label='Enter bio text']").clear()
        page.ele("xpath://textarea[@aria-label='Enter bio text']").input(desc)

        page.eles("xpath://div[@role='dialog']/div[6]//div[@role='button']")[2].click()
        page.wait(0.5)
        page.eles("xpath://div[@role='dialog']//div[@role='button' and @tabindex='0']")[4].click()




    def change_gender(self,page, gender):
        """更改性别方法
        {"女":0,"男":1,"非**":2,"自定义":3}
        """
        self.runto_userhome(page)
        page.get(page.url + "&sk=about")
        page.wait.load_start()

        page.scroll.to_see(page.ele("xpath://a[@role='tab']/span"))
        page.eles("xpath://a[@role='tab']/span")[3].click(by_js=True)


        if page.eles("xpath://div[@role='button' and @data-prevent_chattab_focus='1']"):
            page.eles("xpath://div[@role='button' and @data-prevent_chattab_focus='1']")[1].click()

        page.eles("xpath://div[@role='button']/i")[3].click()

        page.wait.eles_loaded("xpath://div[@role='combobox']//i", timeout=5)
        page.scroll.to_see(page.ele("xpath://div[@role='combobox']//i"))
        page.ele("xpath://div[@role='combobox']//i").click()

        gender_eles = page.eles("xpath://div[@role='option']//span")
        page.wait(0.5)
        gender_eles[gender].click()

        page.eles("xpath://div[@data-pagelet='ProfileAppSection_0']//div[@role='button' and @tabindex='0']//span")[-1].click()


  
    def change_username(self,page, username):
        """更改facebook  username，这个ysername不知道是什么昵称，无法保存"""
        self.user_setting(page)

        page.eles("xpath://div[@role='dialog']//a[@role='link']")[1].click()
        page.wait.load_start()
        page.listen.start("https://accountscenter.facebook.com/api/graphql/")  # 监听账号是否可用

        name_input = page.ele("xpath://label/../input")
        name_input.clear()
        name_input.input(username)

        try:
            data_packet = page.listen.wait(timeout=300)
            if data_packet.response.body['data']['fx_identity_management']['validate_username_v4'][
                'status_code'] != "OK_GENERIC":
                print(data_packet.response.body['data']['fx_identity_management']['validate_username_v4'][
                          'error_message'])
                page.eles("xpath://div[@role='dialog']//div[@role='button']")[0].click()
            else:
                page.ele("xpath://div[@role='dialog']//div[@role='button']//span/span").click()  # 保存
        except Exception as e:
            print("网络异常：五分钟没发出信息包")

 
    def change_name(self,page, name1, name2, name3):
        """更改facebook  name  这个是facebook显示昵称，限制60天内修改一次"""
        self.user_setting(page)

        page.eles("xpath://div[@role='dialog']//a[@role='link']")[0].click()
        page.wait.load_start()

        name_inputs = page.eles("xpath://label/../input")
        name_inputs[0].clear()
        name_inputs[0].input(name1)

        name_inputs[1].clear()
        name_inputs[1].input(name2)

        name_inputs[2].clear()
        name_inputs[2].input(name3)

        page.scroll.to_see("xpath://div[@role='dialog']//div[@role='button']//span/span")
        page.ele("xpath://div[@role='dialog']//div[@role='button']//span/span").click()  # 保存
        page.wait(1)
        page.eles("xpath://div[@role='dialog']//div[@role='button']//span/span")[-1].click()  # 二次保存


if __name__ == '__main__':
    dp = FaceBookDP()
    chrome = Chromium(2222)
    page = chrome.get_tabs()[0]
    photo_path = r"C:\Users\<USER>\Desktop\ayaka.jpg"
    biotext = "ayakaaaaaa"
    gender = 0
    username = 'mumuayakaaaaaaaa'
    name1 = "Desmond"
    name2 = "Mu"
    name3 = "Royce"
    # dp.change_photo(page, photo_path)
    # dp.change_pepBio(page, biotext)
    # dp.change_gender(page, gender)
    dp.change_username(page, username)
    # dp.change_name(page, name1, name2, name3)
