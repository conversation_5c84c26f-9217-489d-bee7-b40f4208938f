from master_control import MasterControl
import queue


def instagram_push_task():
    task = {
        'task_detail_id': '25073010184608735602_instagram_update_name_1001',
        'task_id': '25073010184608735602',
        'task_type': 'update_image',
        'task_client': 'instagram',
        'account_id': '1002',
        'account_ck': 'ckckck',
        'params': {'image_path': r'D:\aa.png'}
    }
    task_queue.put(task)
    task = {
        'task_detail_id': '25073010184608735602_instagram_update_name_1001',
        'task_id': '25073010184608735602',
        'task_type': 'update_desc',
        'task_client': 'instagram',
        'account_id': '1003',
        'account_ck': 'ckckck',
        'params': {'desc': r'taet'}
    }
    task_queue.put(task)
    task = {
        'task_detail_id': '25073010184608735602_instagram_update_name_1001',
        'task_id': '25073010184608735602',
        'task_type': 'update_gender',
        'task_client': 'instagram',
        'account_id': '1004',
        'account_ck': 'ckckck',
        'params': {'gender': 0}
    }
    task_queue.put(task)

    task = {
        'task_detail_id': '25073010184608735602_instagram_update_name_1001',
        'task_id': '25073010184608735602',
        'task_type': 'update_gender',
        'task_client': 'instagram',
        'account_id': '1005',
        'account_ck': 'ckckck',
        'params': {'gender': 0}
    }
    task_queue.put(task)
    task = {
        'task_detail_id': '25073010184608735602_instagram_update_name_1001',
        'task_id': '25073010184608735602',
        'task_type': 'update_gender',
        'task_client': 'instagram',
        'account_id': '1006',
        'account_ck': 'ckckck',
        'params': {'gender': 0}
    }
    task_queue.put(task)
    task = {
        'task_detail_id': '25073010184608735602_instagram_update_name_1001',
        'task_id': '25073010184608735602',
        'task_type': 'update_gender',
        'task_client': 'instagram',
        'account_id': '1007',
        'account_ck': 'ckckck',
        'params': {'gender': 0}
    }
    task_queue.put(task)


def facebook_push_task():
    task = {'task_detail_id': '25073010184608735602_facebook_update_name_1001',
            'task_id': '25073010184608735602',
            'task_type': 'update_name',
            'task_client': 'facebook',
            'account_id': '1001',
            'account_ck': 'ckckck',
            'params': {'new_name': 'hihihi'}
            }
    task_queue.put(task)


def tiktok_push_task():
    task = {'task_detail_id': '25073010184608735602_facebook_update_name_1001',
            'task_id': '25073010184608735602',
            'task_type': 'update_name',
            'task_client': 'tiktok',
            'account_id': '1001',
            'account_ck': 'ckckck',
            'params': {'new_name': 'hihihiasda123'}
            }
    task_queue.put(task)


def twitter_push_task():
    # 任务1：更新用户名 (x1001)
    task1 = {
        'task_detail_id': 'x1001',
        'task_id': 'x1001',
        'task_type': 'update_name',
        'task_client': 'twitter',
        'account_id': 'x1001',
        'account_ck': 'ckckck',
        'params': {'username': 'x1001'}
    }
    task_queue.put(task1)

    # 任务2：更新签名 (x1002)
    task2 = {
        'task_detail_id': 'x1002',
        'task_id': 'x1002',
        'task_type': 'update_desc',
        'task_client': 'twitter',
        'account_id': 'x1002',
        'account_ck': 'ckckck',
        'params': {'desc': '测试签名 x1002   -22222'}
    }
    task_queue.put(task2)

    # 任务3：更新头像 (x1003)
    task3 = {
        'task_detail_id': 'x1003',
        'task_id': 'x1003',
        'task_type': 'update_image',
        'task_client': 'twitter',
        'account_id': 'x1003',
        'account_ck': 'ckckck',
        'params': {'image_path': r'C:\Users\<USER>\Pictures\RqgodvGb_400x400.jpg'}
    }
    task_queue.put(task3)


if __name__ == '__main__':
    cs = MasterControl()
    # 初始化测试队列
    task_queue = queue.Queue()
    cs.tmp_task(task_queue)

    # 平台
    # instagram_push_task()
    # facebook_push_task()
    # tiktok_push_task()
    twitter_push_task()
    cs.monitor_task()
