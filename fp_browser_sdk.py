# 指纹浏览器目录下
# cmd启动指纹浏览器服务（端口自定义）
# 指纹浏览器API接口.exe --remote-debugging-port=2222
import json
import time
import requests


class FingerPrintBrowser:
    def __init__(self):
        self.fp_base_url = "http://localhost:9001/api/browser"
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        # self.proxy_address = "p.webshare.io:80"
        # self.proxy_type = "socks5"
        # self.proxy_username = "aabczpkn-rotate"
        # self.proxy_password = "tzy0nn7slkhy"

        self.proxy_address = ""
        self.proxy_type = ""
        self.proxy_username = ""
        self.proxy_password = ""

    def browser_create(self, title="user_1", base_url="https://www.tiktok.com", size=400, x=0, y=0):
        print(base_url)
        """创建浏览器"""
        url = self.fp_base_url + "/create"
        data = {
            "title": title,  # 窗口标题
            "url": base_url,  # 初始化访问地址
            # "fingerprint": "",
            "fingerprintType": 0,
            "proxyAddress": self.proxy_address,
            "proxyType": self.proxy_type,
            "proxyUsername": self.proxy_username,
            "proxyPassword": self.proxy_password,
            # "debugPort": 7799,    # 无意义（开发问题）
            "x": size * x + (5 * x),
            "y": (size * y) + (35 * y),  # 屏幕位置 y
            "width": size,  # 窗口大小
            "height": size,  # 窗口大小
            "cachePath": title  # 缓存文件：# D:\brower_zw（指纹浏览器路径）\Cache\user_1
        }
        rp = requests.post(url, headers=self.headers, json=data)
        rp_json = rp.json()
        print(f"创建浏览器    ->    {rp_json}")
        browser_id = rp_json["browserId"]
        return browser_id

    def browser_list(self):
        url = self.fp_base_url + "/list"
        rp = requests.get(url, headers=self.headers)
        rp_json = rp.json()
        print(f"浏览器列表    ->    {rp_json}")
        return rp_json

    def browser_set_document_title(self, browser_id, document_title):
        url = self.fp_base_url + "/execute-js"
        data = json.dumps({
            "browserId": str(browser_id),
            "jsCode": f"document.title = '{document_title}'"
        })
        rp = requests.post(url, headers=self.headers, data=data)
        rp_json = rp.json()
        print(f"浏览器列表    ->    {rp_json}")
        return rp_json

    def browser_get_document_title(self, browser_id):
        url = self.fp_base_url + "/execute-js-with-result"
        data = json.dumps({
            "browserId": str(browser_id),
            "jsCode": f"document.title;"
        })
        rp = requests.post(url, headers=self.headers, data=data)
        rp_json = rp.json()
        # {'success': True, 'result': 'user_12', 'errorMessage': None}
        # {'success': True, 'result': 'null', 'errorMessage': None}
        print(f"浏览器列表    ->    {rp_json}")
        return rp_json["result"]

    def load_wait(self, browser_id):
        url = self.fp_base_url + "/execute-js-with-result"
        js_code = "(async ()=>{while(document.readyState!=='complete'){await new Promise(r=>setTimeout(r,500));}true})();"
        data = json.dumps({
            "browserId": str(browser_id),
            "jsCode": js_code
        })
        rp = requests.post(url, headers=self.headers, data=data)
        rp_json = rp.json()
        print(f"load_wait    ->    {rp_json}")
        return rp_json


if __name__ == '__main__':
    mm = FingerPrintBrowser()
    title = "x1003"
    browser_id = mm.browser_create(title=title, base_url="https://httpbin.org/ip")
    # print(browser_id)
    # browser_id = 5
    # mm.browser_list()
    # time.sleep(3)
    # mm.browser_set_document_title(browser_id, title)
    # mm.browser_get_document_title(browser_id)
    # mm.browser_list()
