import time

from DrissionPage import Chromium


class InstagramDP:
    @staticmethod
    def user_setting(page):
        """进入个人中心设置，非ins设置页面
            默认第一个账户，有其它情况另外考虑"""
        page.get("https://accountscenter.instagram.com/profiles/")
        page.wait.load_start()
        page.ele("xpath://div[@role='listitem']").click()
        page.wait.load_start()

    @staticmethod
    def ins_setting_savebutton(page):
        """设置头像,简介的设置页的保存方法
            可能被遮挡，所以用JS方式去点击"""
        page.listen.start("api/v1/web/accounts/edit/")
        page.eles('xpath://div[@role="button" and @tabindex="0"]')[2].click(by_js=True)
        try:
            data_packet = page.listen.wait(timeout=300)
            print("data_packet", data_packet.response.extra_info.all_info.get("statusCode"))
        except Exception as e:
            print("网络异常：五分钟没发出信息包")

    @staticmethod
    def ins_setting(page):
        """进入ins的设置页面"""
        page.get("https://www.instagram.com/accounts/edit/")
        page.wait.load_start()

    def change_profile_photo(self, page, image):
        """修改头像"""
        self.ins_setting(page)
        page.ele("xpath://button[@class='_aadn']").click.to_upload(image)  # 头像更改并上传文件
        result = page.wait.eles_loaded("xpath://div[@role='dialog']//button", timeout=10)
        if result:
            page.eles("xpath://div[@role='dialog']//button")[0].click()  # 有头像需要点击重新上传，没有则不需要点击
        time.sleep(10)

    def change_desc(self, page, desc):
        """修改个人简介"""
        self.ins_setting(page)
        page.ele("xpath://label[@for='pepBio']/following-sibling::textarea").clear()
        page.ele("xpath://label[@for='pepBio']/following-sibling::textarea").input(desc)
        self.ins_setting_savebutton(page)
        time.sleep(3)

    def change_gender(self, page, gender):
        """修改性别方法
            根据匹配数字选择对应的选择按钮{"女":0,"男":1,"不想说":3,"自定义":2}"""
        try:
            self.ins_setting(page)

            page.scroll.to_see(page.ele("xpath://div[@aria-haspopup='menu']"))
            page.ele("xpath://div[@aria-haspopup='menu']").click()

            page.wait.eles_loaded("xpath://div[@role='dialog']//input[@type='checkbox']", timeout=5)
            gender_eles = page.eles("xpath://div[@role='dialog']//input[@type='checkbox']")

            gender_eles[gender].click(by_js=True)
            self.ins_setting_savebutton(page)
        except Exception as e:
            pass
        time.sleep(3)

    def change_username(self, page, username):
        """更改ins用户显示的昵称"""
        self.user_setting(page)

        page.eles("xpath://div[@role='dialog']//a[@role='link']")[1].click()
        page.wait.load_start()
        page.listen.start("https://accountscenter.instagram.com/api/graphql/")  # 监听账号是否可用

        username_input = page.ele("xpath://label/../input")
        username_input.clear()
        username_input.input(username)

        try:
            data_packet = page.listen.wait(timeout=300)
            """帐号不可用，则点击返回，可用则点击保存"""
            if data_packet.response.body['data']['fx_identity_management']['validate_username_v4'][
                'status_code'] != "OK_GENERIC":
                print(data_packet.response.body['data']['fx_identity_management']['validate_username_v4'][
                          'error_message'])
                page.eles("xpath://div[@role='dialog']//div[@role='button']")[0].click()
            else:
                page.ele("xpath://div[@role='dialog']//div[@role='button']//span/span").click()  # 保存
        except Exception as e:
            print("网络异常：五分钟没发出信息包")

    def change_name(self, page, name):
        """更改用户姓名，这个可能是真实姓名，具体不太了解，限制14天内改俩次"""
        self.user_setting(page)

        page.eles("xpath://div[@role='dialog']//a[@role='link']")[0].click()
        page.wait.load_start()

        name_input = page.ele("xpath://label/../input")
        name_input.clear()
        name_input.input(name)

        page.ele("xpath://div[@role='dialog']//div[@role='button']//span/span").click()  # 保存


if __name__ == '__main__':
    dp = InstagramDP()
    browser = Chromium(2222)
    page = browser.get_tabs()[0]
    photo_path = r"C:\Users\<USER>\Desktop\ayaka.jpg"
    biotext = "ayakaaaaaa"
    gender = 1
    username = 'mumuayakaaaaaaaa'
    name = "mumuya"
    dp.change_photo(page, photo_path)
    # dp.change_pepBio(page, biotext)
    # dp.change_gender(page, gender)
    dp.change_username(page, username)
    # dp.change_name(page, name)
