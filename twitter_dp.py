import time
from typing import Dict, Any

from DrissionPage import Chromium
from tenacity import retry, stop_after_attempt, wait_fixed


def return_false_on_failure(_):
    """重试失败后返回False而不是抛出异常"""
    return False


class TwitterDP:

    def __init__(self):
        pass

    def go_to_profile_edit_page(self, page):
        """直接跳转到资料编辑页面"""
        try:
            page.get('https://x.com/settings/profile')
            return True
        except Exception as e:
            print(f"跳转编辑页面时发生错误: {str(e)}")
            return False

    def get_current_profile_info(self, page):
        """获取当前用户的所有配置信息"""
        try:
            profile_info = {}

            # 获取用户名
            name_input = page.ele('xpath://input[@name="displayName"]', timeout=5)
            if name_input:
                profile_info['username'] = name_input.value or ""
            else:
                return None

            # 获取签名/描述
            bio_input = page.ele('xpath://textarea[@name="description"]', timeout=3)
            if bio_input:
                profile_info['desc'] = bio_input.value or ""

            # 获取地点
            location_input = page.ele('xpath://input[@name="location"]', timeout=3)
            if location_input:
                profile_info['location'] = location_input.value or ""

            # 获取网站
            website_input = page.ele('xpath://input[@name="url"]', timeout=3)
            if website_input:
                profile_info['website'] = website_input.value or ""

            # 获取生日信息
            try:
                # 查找生日按钮，生日显示在按钮文本中，格式如 "March 5, 1992"
                birthday_button = page.ele('@@data-testid=pivot', timeout=2)
                if birthday_button:
                    birthday_text = birthday_button.text or ""
                    # 解析生日文本，格式: "Birth date March 5, 1992"
                    if "Birth date" in birthday_text:
                        date_part = birthday_text.replace("Birth date", "").strip()
                        if date_part:
                            profile_info['birthdate'] = date_part

            except Exception:
                pass

            return profile_info

        except Exception:
            pass
            return None

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry_error_callback=return_false_on_failure)
    def change_username(self, page, username: str) -> bool:
        """修改姓名"""
        if not self.go_to_profile_edit_page(page):
            raise Exception('无法跳转到资料编辑页面')

        # 获取当前配置信息并比对
        profile_info = self.get_current_profile_info(page)
        if profile_info is None:
            raise Exception('无法加载编辑页面')

        current_name = profile_info.get('username', '')

        if current_name == username:
            return True

        name_input = page.ele('xpath://input[@name="displayName"]', timeout=3)
        if not name_input:
            raise Exception('未找到姓名输入框')

        time.sleep(0.5)
        name_input.clear()
        name_input.input(username)

        if not self._save_changes(page):
            raise Exception('保存失败')

        return True

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry_error_callback=return_false_on_failure)
    def change_desc(self, page, desc: str) -> bool:
        """修改签名"""
        if not self.go_to_profile_edit_page(page):
            raise Exception('无法跳转到资料编辑页面')

        # 获取当前配置信息并比对
        profile_info = self.get_current_profile_info(page)
        if profile_info is None:
            raise Exception('无法加载编辑页面')

        current_desc = profile_info.get('desc', '')

        if current_desc == desc:
            return True

        bio_input = page.ele('xpath://textarea[@name="description"]', timeout=3)
        if not bio_input:
            raise Exception('未找到签名输入框')

        time.sleep(0.5)
        bio_input.clear()
        bio_input.input(desc)

        if not self._save_changes(page):
            raise Exception('保存失败')

        return True

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry_error_callback=return_false_on_failure)
    def change_location(self, page, location: str) -> bool:
        """修改地点"""
        if not self.go_to_profile_edit_page(page):
            raise Exception('无法跳转到资料编辑页面')

        # 获取当前配置信息并比对
        profile_info = self.get_current_profile_info(page)
        if profile_info is None:
            raise Exception('无法加载编辑页面')

        current_location = profile_info.get('location', '')

        if current_location == location:
            return True

        location_input = page.ele('xpath://input[@name="location"]', timeout=3)
        if not location_input:
            raise Exception('未找到地点输入框')

        time.sleep(0.5)
        location_input.clear()
        location_input.input(location)

        if not self._save_changes(page):
            raise Exception('保存失败')

        return True

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry_error_callback=return_false_on_failure)
    def change_website(self, page, website: str) -> bool:
        """修改网站"""
        if not self.go_to_profile_edit_page(page):
            raise Exception('无法跳转到资料编辑页面')

        # 获取当前配置信息并比对
        profile_info = self.get_current_profile_info(page)
        if profile_info is None:
            raise Exception('无法加载编辑页面')

        current_website = profile_info.get('website', '')

        if current_website == website:
            return True

        website_input = page.ele('xpath://input[@name="url"]', timeout=3)
        if not website_input:
            raise Exception('未找到网站输入框')

        time.sleep(0.5)
        website_input.clear()
        website_input.input(website)

        if not self._save_changes(page):
            raise Exception('保存失败')

        return True

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry_error_callback=return_false_on_failure)
    def change_image(self, page, avatar_path: str) -> bool:
        """修改头像"""
        if not self.go_to_profile_edit_page(page):
            raise Exception('无法跳转到资料编辑页面')

        # 头像无法简单比对，直接执行更新
        profile_info = self.get_current_profile_info(page)
        if profile_info is None:
            raise Exception('无法加载编辑页面')

        file_inputs = page.eles('@@data-testid=fileInput', timeout=3)
        if not file_inputs or len(file_inputs) < 2:
            raise Exception('未找到头像上传控件')

        file_inputs[1].input(avatar_path)  # 使用第2个fileInput

        apply_button = page.ele(
            'xpath://div[@role="dialog"][@aria-modal="true"]//button[@data-testid="applyButton"]', timeout=5)
        if not apply_button:
            raise Exception('未找到应用按钮')

        apply_button.click()

        if not self._save_changes(page):
            raise Exception('保存失败')

        return True

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry_error_callback=return_false_on_failure)
    def change_background(self, page, background_path: str) -> bool:
        """修改背景"""
        if not self.go_to_profile_edit_page(page):
            raise Exception('无法跳转到资料编辑页面')

        # 背景无法简单比对，直接执行更新
        profile_info = self.get_current_profile_info(page)
        if profile_info is None:
            raise Exception('无法加载编辑页面')

        file_inputs = page.eles('@@data-testid=fileInput', timeout=3)
        if not file_inputs:
            raise Exception('未找到背景上传控件')

        file_inputs[0].input(background_path)  # 使用第1个fileInput
        apply_button = page.ele(
            'xpath://div[@role="dialog"][@aria-modal="true"]//button[@data-testid="applyButton"]', timeout=5)
        if not apply_button:
            raise Exception('未找到应用按钮')

        apply_button.click()

        if not self._save_changes(page):
            raise Exception('保存失败')

        return True

    @retry(stop=stop_after_attempt(3), wait=wait_fixed(1), retry_error_callback=return_false_on_failure)
    def change_birthdate(self, page, birthdate_data: Dict[str, Any]) -> bool:
        """修改生日"""
        if not self.go_to_profile_edit_page(page):
            raise Exception('无法跳转到资料编辑页面')

        # 获取当前配置信息并比对
        profile_info = self.get_current_profile_info(page)
        if profile_info is None:
            raise Exception('无法加载编辑页面')

        current_birthdate = profile_info.get('birthdate', '')

        # 比对生日信息 - 将传入的生日数据转换为显示格式进行比对
        if current_birthdate and isinstance(birthdate_data, dict):
            try:
                month_names = ['', 'January', 'February', 'March', 'April', 'May', 'June',
                               'July', 'August', 'September', 'October', 'November', 'December']
                month = birthdate_data.get('month', 0)
                day = birthdate_data.get('day', 0)
                year = birthdate_data.get('year', 0)

                if month and day and year:
                    target_date = f"{month_names[month]} {day}, {year}"
                    if current_birthdate == target_date:
                        # 回到主页
                        page.get('https://x.com')
                        return True
            except:
                pass

        birthday_button = page.ele('@@data-testid=pivot', timeout=5)
        if not birthday_button:
            raise Exception('未找到生日按钮')

        birthday_button.click()

        edit_button = page.ele('@@data-testid=confirmationSheetConfirm', timeout=5)
        if edit_button:
            edit_button.click()

        month_selector = page.ele('@@data-testid=ProfileBirthdate_Month_Selector', timeout=5)
        if not month_selector:
            raise Exception('生日编辑界面加载失败')

        selectors = {
            'month': '@@data-testid=ProfileBirthdate_Month_Selector',
            'day': '@@data-testid=ProfileBirthdate_Day_Selector',
            'year': '@@data-testid=ProfileBirthdate_Year_Selector'
        }

        for field, selector in selectors.items():
            if birthdate_data.get(field):
                element = page.ele(selector)
                if element:
                    value = birthdate_data[field]
                    try:
                        if field == 'month':
                            month_names = ['', 'January', 'February', 'March', 'April', 'May', 'June',
                                           'July', 'August', 'September', 'October', 'November', 'December']
                            month_name = month_names[value]
                            element.select.by_text(month_name)
                        elif field == 'day':
                            element.select.by_value(str(value))
                        else:
                            element.select.by_value(str(value))
                    except:
                        pass

        if not self._save_changes(page):
            raise Exception('保存失败')

        return True

    def _save_changes(self, page) -> bool:
        """保存更改"""
        try:
            save_button = page.ele('xpath://span[text()="Save"]', timeout=5)
            if not save_button:
                save_button = page.ele('xpath://button[contains(text(), "Save")]', timeout=3)

            if not save_button:
                return False

            # 启动网络监听
            page.listen.start("update_profile")
            save_button.click()

            # 检查生日确认对话框
            confirm_dialog = page.ele('@@data-testid=confirmationSheetDialog', timeout=3)
            if confirm_dialog:
                confirm_btn = page.ele('@@data-testid=confirmationSheetConfirm', timeout=2)
                if confirm_btn:
                    confirm_btn.click()

            # 等待网络响应
            try:
                data_packet = page.listen.wait(timeout=300)
                response = data_packet.response

                # 获取响应体
                resp_json = response.body

                # 检查HTTP状态码
                if response.status != 200:
                    return False

                # 检查响应体错误
                if isinstance(resp_json, dict) and 'errors' in resp_json:
                    errors = resp_json.get('errors', [])

                    # 检查账号封禁错误
                    for error in errors:
                        if isinstance(error, dict) and error.get('code') == 64:
                            print("账号被封禁！")
                            return False

                    return False

                return True
            except Exception:
                return False

        except Exception:
            return False


if __name__ == '__main__':
    def setup_cookies(page, cookies: Dict[str, str]):
        """设置登录cookies"""
        try:
            print("正在设置cookies")
            page.get('https://x.com')
            time.sleep(2)

            page.set.cookies(cookies)
            page.refresh()
            return True
        except Exception as e:
            if "Browser context management is not supported" in str(e):
                return True
            else:
                print(f"设置cookies时发生错误: {str(e)}")
                return False


    try:
        # 1. 连接浏览器
        print("\n1. 连接浏览器")
        browser = Chromium(2222)
        tabs = browser.get_tabs()
        target_page = None

        for tab in tabs:
            if 'httpbin.org' in tab.url:
                target_page = tab
                break

        if not target_page:
            print("未找到指定的标签页")
            exit(1)

        print("成功连接到浏览器标签页")

        cookies = {
            'auth_token': 'cf9e2dd13be8034c17f6b2529bdf18552965aa17',
            # 'ct0': '3aad31efeb3c6417dd3e0aacbd5ac9d2401ccdf2aa3d8d37758c0a6f1c429d1ca1ee617e917d50447807b8b793f573b56c08f705e3f0023940f3d359dee1b5e9e8680dd3e2d4b5543459378ad6f6de93'
        }

        if not setup_cookies(target_page, cookies):
            print("cookies设置失败，程序退出")
            exit(1)

        print("cookies设置成功")

        # 2. 创建TwitterDP实例
        print("\n3. 创建TwitterDP实例")
        twitter_dp = TwitterDP()
        print("TwitterDP实例创建成功")

        # 3. 测试各个字段
        print("\n3. 开始测试各个字段")

        # print("\n测试姓名")
        # result = twitter_dp.change_username(target_page, 'Alex Chen')
        # print(f"结果: {result}")
        # time.sleep(2)
        #
        # print("\n测试签名")
        # result = twitter_dp.change_bio(target_page, 'Building the future one line at a time')
        # print(f"结果: {result}")
        # time.sleep(2)
        #
        # print("\n测试地点")
        # result = twitter_dp.change_location(target_page, 'Tokyo, Japan')
        # print(f"结果: {result}")
        # time.sleep(2)
        #
        # print("\n测试网站")
        # result = twitter_dp.change_website(target_page, 'https://alexchen.dev')
        # print(f"结果: {result}")
        # time.sleep(2)
        #
        # print("\n测试头像")
        # result = twitter_dp.change_image(target_page, "assets/d2MJ62N8_400x400.jpg")
        # print(f"结果: {result}")
        # time.sleep(2)
        #
        # print("\n测试背景")
        # result = twitter_dp.change_background(target_page, "assets/background/1500x500 (2).jpg")
        # print(f"结果: {result}")
        # time.sleep(2)
        #
        # print("\n测试生日")
        # result = twitter_dp.change_birthdate(target_page, {'month': 8, 'day': 12, 'year': 1992})
        # print(f"结果: {result}")
        # time.sleep(2)

        print(f"\n{'=' * 50}")

    except Exception as e:
        print(f"❌ 程序执行时发生异常: {str(e)}")
        import traceback

        traceback.print_exc()
