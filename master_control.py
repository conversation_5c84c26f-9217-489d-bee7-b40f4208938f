import queue
import threading
from DrissionPage import Chromium
from urllib.parse import urlparse, parse_qs
from concurrent.futures import ThreadPoolExecutor

from fp_browser_sdk import FingerPrintBrowser
from instagram_dp import InstagramDP
from tiktok_dp import <PERSON>iktokDP
from facebook_dp import FaceBookDP
from twitter_dp import TwitterDP


class MasterControl:
    def __init__(self):
        self.fp_browser = FingerPrintBrowser()
        # self.task_queue = queue.Queue()
        self.instagram_cls = InstagramDP()
        self.facebook_cls = FaceBookDP()
        self.tiktok_cls = TiktokDP()
        self.twitter_cls = TwitterDP()
        self.executor = ThreadPoolExecutor(max_workers=20)
        self.base_url = f"http://127.0.0.1:10089/v1/browser"
        self.pages_dx = {}

        self.account_locks = {}  # account_id: threading.Lock()
        self.lock_global = threading.Lock()  # 保护 account_locks 的创建

        # 指纹浏览器参数
        self.screen_size = 400
        self.screen_index = 0
        self.lock = threading.Lock()  # 初始化一个锁对象
        self.x_max_browser = 4

    def tmp_task(self, queue):
        self.task_queue = queue

    @staticmethod
    def calc_screen(x_max_num=4, index=0):
        x = index % x_max_num
        y = index // x_max_num
        return x, y

    def get_account_lock(self, account_id):
        with self.lock_global:
            if account_id not in self.account_locks:
                self.account_locks[account_id] = threading.Lock()
            return self.account_locks[account_id]

    def __init_dp(self):
        # 不初始化一个、dp无法接管
        browser_list = self.fp_browser.browser_list()
        if not browser_list:
            self.fp_browser.browser_create("base", self.base_url, size=0)
        self.browser_dx = Chromium(2222)

    def start_browser(self, user_id):
        tab_id = self.pages_dx.get(str(user_id), "")
        if tab_id:
            page = self.browser_dx.get_tab(tab_id)
            return page
        url = self.base_url + f"?browser_id={user_id}"
        with self.lock:  # 自动加锁和释放
            screen_x, screen_y = self.calc_screen(x_max_num=self.x_max_browser, index=self.screen_index)
            self.screen_index += 1
        self.fp_browser.browser_create(title=user_id, base_url=url, size=self.screen_size, x=screen_x, y=screen_y)

        for page in self.browser_dx.get_tabs():
            p_url = str(page.url)
            parsed_url = urlparse(p_url)
            query_params = parse_qs(parsed_url.query)
            browser_id = query_params.get('browser_id', [None])[0]
            if user_id == browser_id:
                page_id = str(page.tab_id)
                self.pages_dx[str(user_id)] = page_id
                print(f"绑定：{browser_id} -> {page_id}")
                return page

    def instagram(self, user_id, task_type, params):
        """ins"""
        if task_type == "update_image":
            "头像"
            image = params["image"]
            page_dx = self.start_browser(user_id)
            self.instagram_cls.change_profile_photo(page_dx, image)

        if task_type == "update_desc":
            "简介"
            desc = params["desc"]
            page_dx = self.start_browser(user_id)
            self.instagram_cls.change_desc(page_dx, desc)

        if task_type == "update_gender":
            "性别"
            gender = params["gender"]
            page_dx = self.start_browser(user_id)
            self.instagram_cls.change_gender(page_dx, gender)

    def facebook(self, user_id, task_type, params):
        """脸书"""
        if task_type == "update_image":
            "头像"
            image = params["image"]
            page_dx = self.start_browser(user_id)
            self.facebook_cls.change_profile_photo(page_dx, image)

        if task_type == "update_name":
            "昵称"
            username = params["username"]
            page_dx = self.start_browser(user_id)
            self.facebook_cls.change_username(page_dx, username)

        if task_type == "update_desc":
            "简介"
            desc = params["desc"]
            page_dx = self.start_browser(user_id)
            self.facebook_cls.change_desc(page_dx, desc)

        if task_type == "update_gender":
            "性别"
            gender = params["gender"]
            page_dx = self.start_browser(user_id)
            self.facebook_cls.change_gender(page_dx, gender)

    def tiktok(self, user_id, task_type, params):
        """tiktok"""
        if task_type == "update_image":
            "头像"
            image = params["image_path"]
            page_dx = self.start_browser(user_id)
            self.tiktok_cls.change_profile(page_dx, image)

        if task_type == "update_name":
            "昵称"
            username = params["new_name"]
            page_dx = self.start_browser(user_id)
            self.tiktok_cls.change_username(page_dx, username)

        if task_type == "update_desc":
            "简介"
            desc = params["desc"]
            page_dx = self.start_browser(user_id)
            self.tiktok_cls.change_desc(page_dx, desc)

    def twitter(self, user_id, task_type, params):
        """推特"""
        if task_type == "update_image":
            "头像"
            image_path = params["image_path"]
            page_dx = self.start_browser(user_id)
            self.twitter_cls.change_image(page_dx, image_path)

        if task_type == "update_name":
            "昵称"
            username = params["username"]
            page_dx = self.start_browser(user_id)
            self.twitter_cls.change_username(page_dx, username)

        if task_type == "update_desc":
            "简介"
            desc = params["desc"]
            page_dx = self.start_browser(user_id)
            self.twitter_cls.change_desc(page_dx, desc)

    def thread_main(self, task_client, account_id, task_type, params):
        lock = self.get_account_lock(account_id)
        with lock:
            if task_client == "instagram":
                self.instagram(account_id, task_type, params)
            if task_client == "facebook":
                self.facebook(account_id, task_type, params)
            if task_client == "tiktok":
                self.tiktok(account_id, task_type, params)
            if task_client == "twitter":
                self.twitter(account_id, task_type, params)

    def monitor_task(self):
        self.__init_dp()
        while True:
            task_item = self.task_queue.get(timeout=None)
            task_client = task_item["task_client"]
            account_id = task_item["account_id"]
            task_type = task_item["task_type"]
            params = task_item["params"]
            self.executor.submit(self.thread_main, task_client, account_id, task_type, params)


if __name__ == '__main__':
    mm = MasterControl()
    task_queue = queue.Queue()
    mm.tmp_task(task_queue)
    mm.monitor_task()
